"use client";

import React from "react";

export function CustomButton({ 
  children, 
  variant = "filled", 
  className = "", 
  title,
  ...props 
}) {
  const baseClasses = "group relative overflow-hidden rounded-lg px-6 py-3 font-medium transition-all duration-300 ease-in-out active:scale-95 focus:outline-none";
  
  const variantClasses = {
    filled: "border-none bg-background-primary text-text-primary hover:bg-background-secondary hover:shadow-lg active:bg-background-tertiary focus:ring-2 focus:ring-background-secondary focus:ring-opacity-50",
    transparent: "border border-border-50 bg-transparent text-text-alternative hover:border-text-alternative hover:bg-text-alternative hover:text-text-primary active:bg-background-secondary active:text-text-primary focus:ring-1 focus:ring-text-alternative focus:ring-opacity-50"
  };

  const animationClasses = {
    filled: "absolute inset-0 bg-gradient-to-r from-background-secondary to-background-tertiary transform scale-x-0 origin-left transition-transform duration-300 ease-out group-hover:scale-x-100",
    transparent: "absolute inset-0 bg-text-alternative transform scale-x-0 origin-left transition-transform duration-300 ease-out group-hover:scale-x-100"
  };

  return (
    <button
      title={title}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      {...props}
    >
      <span className="relative z-10 transition-colors duration-300">
        {children}
      </span>
      <div className={animationClasses[variant]}></div>
    </button>
  );
}
